@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 15, 23, 42;
  --background-start-rgb: 15, 32, 39;
  --background-end-rgb: 125, 211, 252;
}

@layer base {
  :root {
    --background: 240 100% 99%;
    --foreground: 210 40% 15%;
    --card: 0 0% 100%;
    --card-foreground: 210 40% 15%;
    --popover: 0 0% 100%;
    --popover-foreground: 210 40% 15%;
    --primary: 188 95% 52%;
    --primary-foreground: 0 0% 100%;
    --secondary: 188 25% 95%;
    --secondary-foreground: 210 40% 15%;
    --muted: 188 25% 95%;
    --muted-foreground: 215 16% 47%;
    --accent: 188 95% 52%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 188 30% 85%;
    --input: 188 30% 90%;
    --ring: 188 95% 52%;
    --chart-1: 188 95% 52%;
    --chart-2: 188 85% 45%;
    --chart-3: 43 96% 56%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-netcare-gradient min-h-screen;
    color: #0F172A; /* netcare-text-primary color directly */
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', 'Open Sans', Arial, sans-serif;
  }
}

@layer components {
  /* Netcare Teal Theme Components */
  .netcare-card {
    @apply bg-netcare-bg-card backdrop-blur-sm border border-netcare-border-light rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .netcare-button {
    @apply bg-netcare-button-gradient hover:bg-netcare-button-primary-hover text-white font-semibold px-6 py-3 rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-105;
    background: linear-gradient(135deg, #0891B2 0%, #0E7490 100%);
  }

  .netcare-input {
    @apply bg-netcare-bg-card border border-netcare-border-light rounded-xl px-4 py-2 placeholder-netcare-text-muted focus:border-netcare-border-accent focus:ring-2 focus:ring-netcare-border-accent/20 transition-all duration-200;
    color: #0F172A; /* netcare-text-primary color directly */
  }

  .text-gradient {
    @apply bg-gradient-to-r from-netcare-button-primary to-netcare-button-secondary bg-clip-text text-transparent;
  }

  .netcare-hero-section {
    @apply bg-netcare-hero-gradient;
  }

  .netcare-section {
    @apply bg-netcare-bg-light;
  }

  /* Header styling */
  .netcare-header {
    @apply bg-netcare-dark-teal/95 backdrop-blur-md border-b border-netcare-cyan/30 shadow-xl;
  }

  /* Header text should be white on dark backgrounds */
  header .text-netcare-white,
  .bg-netcare-navy .text-netcare-white,
  .bg-netcare-navy\/95 .text-netcare-white,
  .bg-netcare-dark-teal .text-netcare-white {
    color: #FFFFFF !important;
  }

  header .text-netcare-white\/80,
  .bg-netcare-navy .text-netcare-white\/80,
  .bg-netcare-navy\/95 .text-netcare-white\/80,
  .bg-netcare-dark-teal .text-netcare-white\/80 {
    color: rgba(255, 255, 255, 0.8) !important;
  }

  header .text-netcare-white\/70,
  .bg-netcare-navy .text-netcare-white\/70,
  .bg-netcare-navy\/95 .text-netcare-white\/70,
  .bg-netcare-dark-teal .text-netcare-white\/70 {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  header .text-netcare-white\/60,
  .bg-netcare-navy .text-netcare-white\/60,
  .bg-netcare-navy\/95 .text-netcare-white\/60,
  .bg-netcare-dark-teal .text-netcare-white\/60 {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  /* Text color utilities */
  .text-netcare-primary {
    color: #0F172A;
  }

  .text-netcare-secondary {
    color: #475569;
  }

  .text-netcare-muted {
    color: #64748B;
  }

  .text-netcare-white {
    color: #FFFFFF;
  }

  .text-netcare-cyan {
    color: #22D3EE;
  }

  /* Override white text on light backgrounds */
  .text-netcare-white {
    color: #0F172A !important; /* Use dark text for better contrast on light backgrounds */
  }

  .text-netcare-white\/80 {
    color: #475569 !important; /* Use secondary dark text */
  }

  .text-netcare-white\/70 {
    color: #64748B !important; /* Use muted dark text */
  }

  .text-netcare-white\/60 {
    color: #64748B !important; /* Use muted dark text */
  }

  /* Status indicators */
  .status-approved {
    @apply bg-netcare-success/10 text-netcare-success border border-netcare-success/20;
  }

  .status-pending {
    @apply bg-netcare-warning/10 text-netcare-warning border border-netcare-warning/20;
  }

  .status-rejected {
    @apply bg-netcare-error/10 text-netcare-error border border-netcare-error/20;
  }

  .status-processing {
    @apply bg-netcare-info/10 text-netcare-info border border-netcare-info/20;
  }

  /* Hover states */
  .hover\:text-netcare-cyan:hover {
    color: #06B6D4;
  }

  .hover\:bg-netcare-cyan:hover {
    background-color: #22D3EE;
  }

  /* Background utilities */
  .bg-netcare-navy {
    background-color: #1B4B5A;
  }

  .bg-netcare-navy\/95 {
    background-color: rgba(27, 75, 90, 0.95);
  }

  .bg-netcare-gold {
    background-color: #22D3EE;
  }

  .bg-white\/10 {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .bg-white\/15 {
    background-color: rgba(255, 255, 255, 0.15);
  }

  /* Border utilities */
  .border-netcare-gold {
    border-color: #22D3EE;
  }

  .border-netcare-gold\/30 {
    border-color: rgba(34, 211, 238, 0.3);
  }

  .border-netcare-gold\/20 {
    border-color: rgba(34, 211, 238, 0.2);
  }

  /* Text color mappings for compatibility */
  .text-cyan-400 {
    color: #22D3EE;
  }

  .text-cyan-300 {
    color: #67E8F9;
  }

  /* Footer text styling */
  footer .text-netcare-white\/60 {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  /* Gradient text on dark backgrounds should be visible */
  .bg-netcare-gradient .text-gradient {
    background: linear-gradient(135deg, #22D3EE 0%, #06B6D4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Professional healthcare styling */
  .healthcare-professional {
    @apply transition-all duration-300 hover:shadow-lg;
  }

  .healthcare-card-hover {
    @apply hover:scale-102 hover:shadow-xl transition-all duration-300;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Shadow utilities */
  .shadow-netcare {
    box-shadow: 0 4px 6px -1px rgba(34, 211, 238, 0.1), 0 2px 4px -1px rgba(34, 211, 238, 0.06);
  }

  .shadow-netcare-lg {
    box-shadow: 0 10px 15px -3px rgba(34, 211, 238, 0.1), 0 4px 6px -2px rgba(34, 211, 238, 0.05);
  }

  .shadow-netcare-xl {
    box-shadow: 0 20px 25px -5px rgba(34, 211, 238, 0.1), 0 10px 10px -5px rgba(34, 211, 238, 0.04);
  }
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(224, 247, 250, 0.3);
}

::-webkit-scrollbar-thumb {
  background: #22D3EE;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #06B6D4;
}

/* Smooth scale utilities */
.scale-102 {
  transform: scale(1.02);
}
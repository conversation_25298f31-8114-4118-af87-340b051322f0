@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 29, 52, 67;
  --background-start-rgb: 248, 246, 244;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 15, 23, 42;
    --background-end-rgb: 2, 6, 23;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 210 25% 20%;
    --card: 0 0% 100%;
    --card-foreground: 210 25% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 210 25% 20%;
    --primary: 185 45% 55%;
    --primary-foreground: 0 0% 98%;
    --secondary: 210 25% 95%;
    --secondary-foreground: 210 25% 20%;
    --muted: 210 25% 95%;
    --muted-foreground: 0 0% 45%;
    --accent: 185 45% 55%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 25% 87%;
    --input: 210 25% 90%;
    --ring: 185 45% 55%;
    --chart-1: 185 45% 55%;
    --chart-2: 210 50% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 210 25% 8%;
    --foreground: 0 0% 98%;
    --card: 210 25% 12%;
    --card-foreground: 0 0% 98%;
    --popover: 210 25% 12%;
    --popover-foreground: 0 0% 98%;
    --primary: 185 45% 55%;
    --primary-foreground: 0 0% 9%;
    --secondary: 210 25% 16%;
    --secondary-foreground: 0 0% 98%;
    --muted: 210 25% 16%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 185 45% 55%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 25% 20%;
    --input: 210 25% 16%;
    --ring: 185 45% 55%;
    --chart-1: 185 45% 55%;
    --chart-2: 210 50% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-netcare-gradient min-h-screen;
    color: #1D3443; /* netcare-light-text color directly */
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Open Sans', Arial, sans-serif;
  }
}

@layer components {
  /* Light theme styles (default) */
  .netcare-card {
    @apply bg-netcare-light-card backdrop-blur-sm border border-netcare-light-border rounded-xl shadow-netcare;
  }
  
  .netcare-button {
    @apply bg-netcare-primary-blue hover:bg-netcare-secondary-blue text-white font-semibold px-6 py-3 rounded-xl transition-all duration-300 hover:shadow-netcare-lg hover:scale-105;
  }
  
  .netcare-input {
    @apply bg-netcare-light-card border border-netcare-light-border rounded-xl px-4 py-2 placeholder-netcare-light-text-secondary focus:border-netcare-light-accent focus:ring-2 focus:ring-netcare-light-accent/20;
    color: #1D3443; /* netcare-light-text color directly */
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-netcare-primary-blue to-netcare-secondary-blue bg-clip-text text-transparent;
  }

  .netcare-hero-section {
    @apply bg-netcare-hero-gradient;
  }

  .netcare-section {
    @apply bg-netcare-light-bg;
  }

  /* Dark theme overrides */
  .dark .netcare-card {
    @apply bg-white/10 border border-netcare-gold/20 rounded-xl shadow-xl;
  }
  
  .dark .netcare-button {
    @apply bg-gold-gradient text-netcare-navy font-semibold px-6 py-3 rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-105;
  }
  
  .dark .netcare-input {
    @apply bg-white/10 border border-netcare-gold/30 rounded-xl px-4 py-2 focus:border-netcare-gold focus:ring-2 focus:ring-netcare-gold/20;
    color: #ffffff; /* text-netcare-white color directly */
  }

  .dark .netcare-input::placeholder {
    color: rgba(255, 255, 255, 0.6); /* placeholder-netcare-white/60 color directly */
  }

  .text-gradient {
    @apply bg-gold-gradient bg-clip-text text-transparent;
  }

  /* Dark mode background */
  .dark .bg-netcare-gradient {
    @apply bg-gradient-to-br from-netcare-navy to-netcare-dark-navy;
  }

  /* Dark mode header */
  .dark header {
    @apply bg-netcare-navy/95 backdrop-blur-md border-b border-netcare-gold/30 shadow-xl;
  }

  /* Dark mode text colors */
  .dark body {
    color: #ffffff;
  }

  .dark .text-netcare-light-text-secondary {
    color: rgba(255, 255, 255, 0.7); /* text-netcare-white/70 color directly */
  }

  .dark .text-netcare-primary-blue {
    @apply text-cyan-400;
  }

  .dark .text-netcare-secondary-blue {
    @apply text-cyan-300;
  }

  /* Dark mode hover states */
  .dark .hover\:text-netcare-primary-blue:hover {
    color: #66B6C4; /* netcare-gold color directly */
  }

  /* Dark mode backgrounds */
  .dark .bg-netcare-light-card {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .dark .bg-netcare-light-bg {
    @apply bg-netcare-navy/50;
  }

  /* Light mode specific overrides for existing dark theme components */
  .text-netcare-white {
    color: #1D3443; /* netcare-light-text color directly */
  }

  .text-netcare-white\/70 {
    color: #57656E; /* netcare-light-text-secondary color directly */
  }

  .text-netcare-white\/60 {
    color: #57656E; /* netcare-light-text-secondary color directly */
  }

  .text-netcare-white\/80 {
    color: #1D3443; /* netcare-light-text color directly */
  }

  .text-netcare-gold {
    color: #66B6C4; /* netcare-primary-blue color directly */
  }

  /* Light mode hover states */
  .hover\:text-netcare-gold:hover {
    color: #53A6B6; /* netcare-secondary-blue color directly */
  }

  /* Light mode borders */
  .border-netcare-gold\/30 {
    @apply border-netcare-light-border;
  }

  .border-netcare-gold\/20 {
    @apply border-netcare-light-border;
  }

  .border-netcare-gold {
    @apply border-netcare-primary-blue;
  }

  /* Light mode backgrounds */
  .bg-netcare-navy\/95 {
    @apply bg-netcare-light-card/95 backdrop-blur-md;
  }

  .bg-netcare-navy\/50 {
    @apply bg-netcare-light-border;
  }



  .bg-white\/15 {
    @apply bg-netcare-gainsboro;
  }

  /* Light mode logo background */
  .bg-netcare-navy {
    @apply bg-netcare-light-text;
  }

  .bg-netcare-gold {
    @apply bg-netcare-primary-blue;
  }

  /* Additional light mode color mappings */
  .text-cyan-400 {
    color: #66B6C4; /* netcare-primary-blue color directly */
  }

  .text-cyan-300 {
    color: #53A6B6; /* netcare-secondary-blue color directly */
  }

  .bg-cyan-400\/20 {
    @apply bg-netcare-primary-blue/20;
  }

  .bg-cyan-400\/10 {
    @apply bg-netcare-primary-blue/10;
  }

  .border-cyan-400 {
    @apply border-netcare-primary-blue;
  }

  .hover\:bg-cyan-400:hover {
    @apply hover:bg-netcare-primary-blue;
  }

  .hover\:text-cyan-400:hover {
    color: #66B6C4; /* netcare-primary-blue color directly */
  }

  .hover\:text-cyan-300:hover {
    color: #53A6B6; /* netcare-secondary-blue color directly */
  }

  /* Professional healthcare styling */
  .healthcare-professional {
    @apply transition-all duration-300 hover:shadow-netcare-lg;
  }

  .healthcare-card-hover {
    @apply hover:scale-102 hover:shadow-netcare-xl transition-all duration-300;
  }

  /* Override for dark mode using custom utility classes instead of targeting escaped utility names */
  /* Removed utilities with escaped characters that cause circular dependencies */
  
  /* Custom classes for dark mode that don't use escaped utility names */
  .dark .netcare-text-white-70 {
    color: rgba(255, 255, 255, 0.70);
  }

  .dark .netcare-text-white-60 {
    color: rgba(255, 255, 255, 0.60);
  }

  .dark .netcare-text-white-80 {
    color: rgba(255, 255, 255, 0.80);
  }

  /* Custom gold text colors - using custom class names to avoid circular deps */
  .dark .netcare-text-gold {
    color: #E6BE8A;
  }

  .dark .netcare-hover\:text-gold:hover {
    color: #ffd700;
  }

  /* Removed dark mode overrides that were causing circular dependencies */
  /* Instead, use custom classes for dark mode or handle through Tailwind config */

  /* Custom dark mode background colors */
  .dark .netcare-bg-cyan-400-20 {
    background-color: rgba(34, 211, 238, 0.2);
  }

  .dark .netcare-bg-cyan-400-10 {
    background-color: rgba(34, 211, 238, 0.1);
  }

  /* Custom dark mode border colors */
  .dark .netcare-border-cyan-400 {
    border-color: #22d3ee;
  }

  /* Custom hover states */
  .dark .netcare-hover\:bg-cyan-400:hover {
    background-color: #22d3ee;
  }

  .dark .netcare-hover\:text-cyan-400:hover {
    color: #22d3ee;
  }

  .dark .netcare-hover\:text-cyan-300:hover {
    color: #67e8f9;
  }

  
  /* Custom scrollbar for dark mode */
  .dark ::-webkit-scrollbar {
    width: 8px;
  }
}

::-webkit-scrollbar-track {
  background: rgba(211, 231, 236, 0.3);
}

::-webkit-scrollbar-thumb {
  background: #66B6C4;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #53A6B6;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: rgba(30, 58, 95, 0.3);
}

.dark ::-webkit-scrollbar-thumb {
  background: #f4d03f;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #f7dc6f;
}

/* Smooth scale utilities */
.scale-102 {
  transform: scale(1.02);
}
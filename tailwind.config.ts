import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'netcare-gradient': 'linear-gradient(135deg, #F8F6F4 0%, #FFFFFF 50%, #E0F0F3 100%)',
        'netcare-hero-gradient': 'linear-gradient(135deg, #BEE2F0 0%, #D2EAEE 50%, #FFFFFF 100%)',
        'gold-gradient': 'linear-gradient(135deg, #66B6C4 0%, #53A6B6 100%)',
        'netcare-card-gradient': 'linear-gradient(135deg, #FFFFFF 0%, #F7F7F7 100%)',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        // Netcare Brand Colors from the palette
        netcare: {
          // Primary brand colors
          'midnight-blue': '#002663',
          'dodger-blue': '#007BFF',
          'primary-blue': '#66B6C4',
          'secondary-blue': '#53A6B6',
          'light-blue': '#A3D3DC',
          'powder-blue': '#BEE2F0',
          
          // Dark colors
          'dark-slate': '#152734',
          'dark-slate-2': '#1C3444', 
          'dark-slate-3': '#1D3443',
          'charcoal': '#222222',
          'steel-gray': '#325266',
          
          // Medium colors
          'steel-blue': '#388EA2',
          'cadet-blue': '#53A6B6',
          'dim-gray': '#57656E',
          'dim-gray-2': '#61717B',
          'aquamarine': '#66B6C4',
          'warm-gray': '#7F6F64',
          'gray': '#808080',
          'slate-gray': '#80898F',
          
          // Light colors
          'silver': '#BEC2C6',
          'gainsboro': '#D1E9ED',
          'lavender': '#D2EAEE',
          'gainsboro-2': '#D3E7EC',
          'lavender-2': '#E0F0F3',
          'beige': '#E4D7CC',
          'lavender-3': '#E8E9EA',
          'lavender-4': '#EBEDED',
          'white-smoke': '#ECF0F1',
          'white-smoke-2': '#F7F7F7',
          'cream': '#F8F6F4',
          
          // Semantic colors
          white: '#FFFFFF',
          navy: '#1D3443',
          'dark-navy': '#152734',
          gold: '#66B6C4',
          'light-gold': '#A3D3DC',
          bronze: '#388EA2',
          
          // Light mode specific
          'light-bg': '#F8F6F4',
          'light-card': '#FFFFFF',
          'light-text': '#1D3443',
          'light-text-secondary': '#57656E',
          'light-border': '#D3E7EC',
          'light-accent': '#66B6C4',
          'light-accent-hover': '#53A6B6',
          'light-accent-dark': '#388EA2',
          'light-button': '#1D3443',
          'light-button-hover': '#152734',
          'light-success': '#66B6C4',
          'light-warning': '#7F6F64',
          'light-error': '#002663',
        },
        // Shadcn UI Colors
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
        'fade-in': {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        'slide-in': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        'gentle-float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'subtle-glow': {
          '0%, 100%': { boxShadow: '0 0 20px rgba(102, 182, 196, 0.3)' },
          '50%': { boxShadow: '0 0 30px rgba(102, 182, 196, 0.5)' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'fade-in': 'fade-in 0.6s ease-out',
        'slide-in': 'slide-in 0.5s ease-out',
        'gentle-float': 'gentle-float 6s ease-in-out infinite',
        'subtle-glow': 'subtle-glow 4s ease-in-out infinite',
      },
      fontFamily: {
        'sans': ['Open Sans', 'Arial', 'sans-serif'],
      },
      boxShadow: {
        'netcare': '0 4px 20px rgba(102, 182, 196, 0.1)',
        'netcare-lg': '0 8px 40px rgba(102, 182, 196, 0.15)',
        'netcare-xl': '0 12px 60px rgba(102, 182, 196, 0.2)',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
export default config;